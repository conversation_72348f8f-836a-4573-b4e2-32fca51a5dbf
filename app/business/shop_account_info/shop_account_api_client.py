"""
店铺账户信息API客户端

基于BaseYimaiClient实现的专门用于店铺账户信息查询的API客户端。
主要功能：
1. 订单列表查询 - getOrderList API
2. 订单详情查询 - getOrderDetail API  
3. 集成用户信息提取和Token管理
4. 支持分页查询和数据过滤

API接口：
- 订单列表: POST https://dcmmaster.yibainetwork.com/orders/order/getOrderList
- 订单详情: GET https://dcmmaster.yibainetwork.com/orders/order/getOrderDetail
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlencode

from app.shared.clients.base_yimai_client import BaseYimaiClient
from app.shared.utils.auth_manager import UniversalAuthManager
from app.core.web_driver.base_driver import BaseWebDriver


class ShopAccountApiClient(BaseYimaiClient):
    """
    店铺账户信息API客户端
    
    继承BaseYimaiClient，专门用于店铺账户信息相关的API调用
    """
    
    def __init__(self, logger: logging.Logger = None, business_type: str = None, script_name: str = None):
        """
        初始化店铺账户信息API客户端
        
        Args:
            logger: 日志记录器
            business_type: 业务类型
            script_name: 脚本名称
        """
        # 店铺账户信息专用配置
        config = {
            'base_url': 'https://dcmmaster.yibainetwork.com',
            'request_timeout': 60,
            'max_retries': 3,
            'retry_delay': 2,
            
            # 专用请求头配置
            'common_headers': {
                'authority': 'dcmmaster.yibainetwork.com',
                'method': 'POST',
                'scheme': 'https',
                'accept': 'application/json, text/plain, */*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/x-www-form-urlencoded',
                'origin': 'https://dcmmaster.yibainetwork.com',
                'referer': 'https://dcmmaster.yibainetwork.com/',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        }
        
        super().__init__(logger, config)
        
        self.business_type = business_type
        self.script_name = script_name
        
        # 初始化通用认证管理器（基于block_manager的成熟实现）
        self.auth_manager = UniversalAuthManager(
            self.logger, self.business_type, self.script_name
        )
        
        # 认证信息缓存
        self.auth_info = {
            'tokens': None,
            'user_info': None,
            'authenticated': False
        }

        # 保存driver引用，用于重新认证
        self._driver = None
        
        self.logger.info("✅ 店铺账户信息API客户端初始化完成", extra_data={
            'business_type': self.business_type,
            'script_name': self.script_name,
            'base_url': self.config['base_url']
        })
    
    async def authenticate_and_get_user_info(self, driver: BaseWebDriver) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        登录验证并获取用户信息

        Args:
            driver: Web驱动实例

        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]: Token字典和用户信息字典
        """
        try:
            self.logger.info("🚀 开始一站式登录验证和认证信息提取")

            # 使用基于block_manager成熟实现的通用认证管理器
            tokens, user_info = await self.auth_manager.authenticate_and_extract_all(driver)

            if not tokens or not user_info:
                self.logger.error("❌ 登录验证或认证信息提取失败")
                return None, None

            # 缓存认证信息
            self.auth_info = {
                'tokens': tokens,
                'user_info': user_info,
                'authenticated': True
            }

            self.logger.info(f"✅ 一站式认证信息获取成功 - 用户ID: {user_info.get('uid')}, "
                           f"分销商ID: {user_info.get('distributor_id')}, 账户: {user_info.get('account')}")

            return tokens, user_info

        except Exception as e:
            error_msg = f"一站式登录验证和认证信息提取异常: {str(e)}"
            self.logger.error(error_msg)
            return None, None

    def set_driver(self, driver: BaseWebDriver):
        """
        设置Web驱动引用，用于重新认证

        Args:
            driver: Web驱动实例
        """
        self._driver = driver

    async def get_order_list(self,
                            sku: str,
                            creation_time: str,
                            tokens: Dict[str, str],
                            user_info: Dict[str, Any],
                            page: int = 1,
                            limit: int = 20,
                            size: int = 20) -> Dict[str, Any]:
        """
        获取订单列表（带自动重新认证）

        Args:
            sku: 系统SKU
            creation_time: 创建时间（用于计算paytime范围）
            tokens: 认证Token字典
            user_info: 用户信息字典
            page: 页码
            limit: 每页限制
            size: 页面大小

        Returns:
            Dict[str, Any]: 订单列表响应
        """
        return await self._retry_with_reauth(
            self._get_order_list_impl,
            sku=sku,
            creation_time=creation_time,
            tokens=tokens,
            user_info=user_info,
            page=page,
            limit=limit,
            size=size
        )

    async def _get_order_list_impl(self,
                                  sku: str,
                                  creation_time: str,
                                  tokens: Dict[str, str],
                                  user_info: Dict[str, Any],
                                  page: int = 1,
                                  limit: int = 20,
                                  size: int = 20) -> Dict[str, Any]:
        """
        获取订单列表的具体实现
        """
        try:
            self.logger.info(f"获取订单列表 - SKU: {sku}, 页码: {page}")

            # 计算paytime范围（创建时间前后3天）
            creation_dt = datetime.strptime(creation_time, "%Y-%m-%d %H:%M:%S")
            start_time = creation_dt - timedelta(days=3)
            end_time = creation_dt + timedelta(days=3)
            paytime = f"{start_time.strftime('%Y-%m-%d %H:%M:%S')}-{end_time.strftime('%Y-%m-%d %H:%M:%S')}"

            # 构建请求数据
            data = {
                'paytime': paytime,
                'sku': sku,
                'distributor_id': user_info.get('distributor_id', ''),
                'uid': user_info.get('uid', ''),
                'system_type': '2',  # 固定值
                'source_from': '1',  # 固定值
                'page': str(page),
                'limit': str(limit),
                'size': str(size)
            }

            # API端点
            endpoint = f"{self.config['base_url']}/orders/order/getOrderList"

            # 执行POST请求
            response = await self.post_request(
                endpoint=endpoint,
                data=data,
                tokens=tokens,
                endpoint_path='/orders/order/getOrderList',
                current_route='/orders/order/getOrderList'
            )

            self.logger.info(f"订单列表获取成功 - 页码: {page}", extra_data={
                'sku': sku,
                'page': page,
                'response_code': response.get('code'),
                'total_orders': response.get('data', {}).get('page_data', {}).get('total', 0)
            })

            return response

        except Exception as e:
            self.logger.error(f"获取订单列表异常: {str(e)}", extra_data={
                'sku': sku,
                'page': page,
                'exception_type': type(e).__name__
            })
            raise
    
    async def get_order_detail(self,
                              order_id: str,
                              tokens: Dict[str, str],
                              user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取订单详情（带自动重新认证）

        Args:
            order_id: 订单ID
            tokens: 认证Token字典
            user_info: 用户信息字典

        Returns:
            Dict[str, Any]: 订单详情响应
        """
        return await self._retry_with_reauth(
            self._get_order_detail_impl,
            order_id=order_id,
            tokens=tokens,
            user_info=user_info
        )

    async def _get_order_detail_impl(self,
                                    order_id: str,
                                    tokens: Dict[str, str],
                                    user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取订单详情的具体实现
        """
        try:
            self.logger.info(f"获取订单详情 - 订单ID: {order_id}")

            # 构建查询参数
            params = {
                'order_id': order_id,
                'uid': user_info.get('uid', ''),
                'source_from': '1'
            }

            # API端点
            endpoint = f"{self.config['base_url']}/orders/order/getOrderDetail"

            # 执行GET请求
            response = await self.get_request(
                endpoint=endpoint,
                params=params,
                tokens=tokens,
                additional_headers={
                    'method': 'GET',
                    'path': '/orders/order/getOrderDetail'
                }
            )

            self.logger.info(f"订单详情获取成功 - 订单ID: {order_id}", extra_data={
                'order_id': order_id,
                'response_code': response.get('code')
            })

            return response

        except Exception as e:
            self.logger.error(f"获取订单详情异常: {str(e)}", extra_data={
                'order_id': order_id,
                'exception_type': type(e).__name__
            })
            raise
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证
        
        Returns:
            bool: 是否已认证
        """
        return self.auth_info.get('authenticated', False)
    
    def get_cached_auth_info(self) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        获取缓存的认证信息

        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, str]], Optional[Dict[str, Any]]]: Token字典和用户信息字典
        """
        if self.is_authenticated():
            return self.auth_info['tokens'], self.auth_info['user_info']
        return None, None

    def _is_auth_error(self, exception: Exception) -> bool:
        """
        判断异常是否为认证错误

        Args:
            exception: 异常对象

        Returns:
            bool: 是否为认证错误
        """
        error_message = str(exception)
        return "AUTH_FAILED" in error_message

    async def _handle_auth_failure(self) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        处理认证失败，重新进行认证

        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]: 新的Token字典和用户信息字典
        """
        try:
            if not self._driver:
                self.logger.error("❌ 没有可用的driver进行重新认证")
                return None, None

            self.logger.info("🔄 检测到认证失败，开始重新认证")

            # 清除旧的认证信息
            self.auth_info = {
                'tokens': None,
                'user_info': None,
                'authenticated': False
            }

            # 重新进行认证
            tokens, user_info = await self.authenticate_and_get_user_info(self._driver)

            if tokens and user_info:
                self.logger.info("✅ 重新认证成功")
                return tokens, user_info
            else:
                self.logger.error("❌ 重新认证失败")
                return None, None

        except Exception as e:
            self.logger.error(f"重新认证过程异常: {str(e)}")
            return None, None

    async def _retry_with_reauth(self, func, *args, **kwargs):
        """
        带重新认证的重试机制

        Args:
            func: 要重试的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            函数执行结果
        """
        max_auth_retries = 2  # 最大认证重试次数

        for attempt in range(max_auth_retries + 1):
            try:
                return await func(*args, **kwargs)

            except Exception as e:
                if self._is_auth_error(e) and attempt < max_auth_retries:
                    self.logger.warning(f"第{attempt + 1}次尝试认证失败，准备重新认证: {str(e)}")

                    # 重新认证
                    new_tokens, new_user_info = await self._handle_auth_failure()

                    if new_tokens and new_user_info:
                        # 更新kwargs中的tokens和user_info
                        if 'tokens' in kwargs:
                            kwargs['tokens'] = new_tokens
                        if 'user_info' in kwargs:
                            kwargs['user_info'] = new_user_info

                        self.logger.info(f"认证更新完成，准备第{attempt + 2}次尝试")
                        continue
                    else:
                        self.logger.error("重新认证失败，无法继续重试")
                        raise
                else:
                    # 非认证错误或重试次数已用完
                    raise

        raise Exception("重试次数已用完，操作失败")
