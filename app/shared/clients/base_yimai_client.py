"""
通用亿迈系统HTTP客户端基类

提供亿迈系统HTTP接口调用的基础功能：
1. 通用请求方法和认证处理
2. 标准请求头和参数构建
3. 错误处理和重试机制
4. 支持继承扩展具体业务接口

重构说明：
- 基于export_publish_list/export_client.py重构
- 抽离通用HTTP操作和认证逻辑
- 支持子类扩展具体业务接口
"""

import asyncio
import aiohttp
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlencode, quote
from pathlib import Path
import tempfile


class BaseYimaiClient:
    """
    亿迈系统HTTP客户端基类
    
    提供通用的HTTP接口调用功能：
    - 统一的请求方法
    - 标准认证处理  
    - 错误处理和重试
    - 文件下载支持
    """
    
    def __init__(self, logger: logging.Logger = None, config: Dict[str, Any] = None):
        """
        初始化亿迈系统HTTP客户端
        
        Args:
            logger: 日志记录器
            config: 自定义配置，可覆盖默认配置
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.config = self._get_default_config()
        
        # 合并自定义配置
        if config:
            self.config.update(config)
        
        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=self.config['request_timeout'])
        self.max_retries = self.config['max_retries']
        self.retry_delay = self.config['retry_delay']
        
        self.logger.info("亿迈系统HTTP客户端基类初始化完成", extra_data={
            'base_url': self.config['base_url'],
            'request_timeout': self.config['request_timeout'],
            'max_retries': self.max_retries
        })
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 基础配置
            'base_url': 'https://salecentersaasapi.yibainetwork.com',
            'request_timeout': 60,
            'download_timeout': 1200,  # 增加下载超时时间到20分钟，解决大文件和网络慢的问题
            'max_retries': 3,
            'retry_delay': 2,
            
            # 文件下载专用配置
            'download_max_retries': 5,  # 文件下载专用重试次数
            'download_retry_delay': 5,  # 文件下载重试间隔（秒）
            'download_chunk_size': 8192,  # 下载块大小
            'max_file_size_mb': 512,  # 最大文件大小限制（MB）
            
            # 通用请求头
            'common_headers': {
                'accept': 'application/json, text/plain, */*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
                'origin': 'https://salecentersaas.yibainetwork.com',
                'referer': 'https://salecentersaas.yibainetwork.com/',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not(A:Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        }
    
    def build_headers(self, 
                     tokens: Dict[str, str], 
                     endpoint_path: str = '',
                     current_route: str = '',
                     loading_type: str = 'showLoading',
                     additional_headers: Dict[str, str] = None) -> Dict[str, str]:
        """
        构建标准请求头
        
        Args:
            tokens: 认证Token字典
            endpoint_path: API端点路径
            current_route: 当前路由
            loading_type: 加载类型
            additional_headers: 额外的请求头
            
        Returns:
            Dict[str, str]: 构建的请求头
        """
        headers = self.config['common_headers'].copy()
        
        # 添加JWT Token到Authorization头
        if tokens and 'jwt_token' in tokens and tokens['jwt_token']:
            headers['Authorization'] = tokens['jwt_token']
        
        # 设置认证相关头部
        headers.update({
            'devicenumber': tokens.get('device_number', ''),
            'token1-check': tokens.get('token1_check', ''),
            'token2': tokens.get('token2', ''),
            'token2-timestamp': tokens.get('token2_timestamp', ''),
            'currentroute': current_route,
            'loadingtype': loading_type,
            'Scd': tokens.get('device_number', '')  # 从截图中看到Scd头也是必要的
        })
        
        # 添加anticlimb_verify_code如果存在
        if tokens and 'anticlimb_verify_code' in tokens and tokens['anticlimb_verify_code']:
            headers['Sys'] = tokens['anticlimb_verify_code']
        
        # 设置动态路径
        if endpoint_path:
            headers['path'] = endpoint_path
        
        # 合并额外的请求头
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def build_payload(self, 
                     data: Dict[str, Any], 
                     tokens: Dict[str, str]) -> str:
        """
        构建标准请求载荷
        
        Args:
            data: 请求数据
            tokens: 认证Token字典
            
        Returns:
            str: URL编码的请求载荷
        """
        # 添加认证信息
        payload_data = data.copy()
        payload_data.update({
            'jwt_token': tokens.get('jwt_token', ''),
            'anticlimb_verify_code': tokens.get('anticlimb_verify_code', '')
        })
        
        # URL编码
        return urlencode(payload_data)

    def _is_auth_failed_response(self, response_data: Dict[str, Any]) -> bool:
        """
        检测API响应是否表示登录失效

        检测条件：
        - status: 0
        - errorCode: "E4002"
        - http_status_code: 401
        - errorMess包含"登录状态失效"等关键词

        Args:
            response_data: API响应数据

        Returns:
            bool: 是否为登录失效响应
        """
        try:
            if not isinstance(response_data, dict):
                return False

            # 检查status字段
            status = response_data.get('status')
            if status != 0:
                return False

            # 检查errorCode字段
            error_code = response_data.get('errorCode')
            if error_code == 'E4002':
                self.logger.debug("检测到登录失效响应：errorCode为E4002")
                return True

            # 检查http_status_code字段
            http_status_code = response_data.get('http_status_code')
            if http_status_code == 401:
                self.logger.debug("检测到登录失效响应：http_status_code为401")
                return True

            # 检查错误信息关键词
            error_message = response_data.get('errorMess', '') or response_data.get('msg', '')
            auth_failure_keywords = [
                '登录状态失效',
                '请重新登录',
                '未登录',
                '登录失效',
                '身份验证失败',
                'authentication failed',
                'login required',
                'unauthorized'
            ]

            if error_message:
                error_message_lower = error_message.lower()
                for keyword in auth_failure_keywords:
                    if keyword.lower() in error_message_lower:
                        self.logger.debug(f"检测到登录失效响应：错误信息包含关键词'{keyword}'")
                        return True

            return False

        except Exception as e:
            self.logger.debug(f"检测登录失效响应时异常: {str(e)}")
            return False

    async def post_request(self,
                          endpoint: str,
                          data: Dict[str, Any],
                          tokens: Dict[str, str],
                          endpoint_path: str = '',
                          current_route: str = '',
                          additional_headers: Dict[str, str] = None,
                          retries: int = None) -> Dict[str, Any]:
        """
        执行POST请求
        
        Args:
            endpoint: 完整的API端点URL
            data: 请求数据
            tokens: 认证Token字典
            endpoint_path: API端点路径（用于请求头）
            current_route: 当前路由
            additional_headers: 额外的请求头
            retries: 重试次数，None使用默认值
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        if retries is None:
            retries = self.max_retries
        
        last_exception = None
        
        for attempt in range(retries + 1):
            try:
                self.logger.info(f"执行POST请求 (第{attempt + 1}次尝试)", extra_data={
                    'endpoint': endpoint,
                    'data_keys': list(data.keys()),
                    'attempt': attempt + 1,
                    'max_attempts': retries + 1
                })
                
                # 构建请求头和载荷
                headers = self.build_headers(
                    tokens=tokens,
                    endpoint_path=endpoint_path,
                    current_route=current_route,
                    additional_headers=additional_headers
                )
                payload = self.build_payload(data, tokens)
                
                # 构建Cookie
                cookies = {}
                if 'cookies' in tokens:
                    cookies = tokens['cookies']
                    self.logger.debug("使用Token中的Cookie", extra_data={
                        'cookie_keys': list(cookies.keys()) if cookies else []
                    })
                
                # 发送请求
                async with aiohttp.ClientSession(timeout=self.timeout) as session:
                    async with session.post(
                        endpoint,
                        data=payload,
                        headers=headers,
                        cookies=cookies
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()

                            # 检测登录失效
                            if self._is_auth_failed_response(result):
                                self.logger.warning("🔐 检测到登录失效响应", extra_data={
                                    'endpoint': endpoint,
                                    'response_status': result.get('status'),
                                    'error_code': result.get('errorCode'),
                                    'error_message': result.get('errorMess', result.get('msg'))
                                })
                                # 抛出特殊异常，便于上层处理
                                raise Exception(f"AUTH_FAILED: 登录状态失效，需要重新认证 - {result.get('errorMess', result.get('msg', ''))}")

                            self.logger.info("POST请求成功", extra_data={
                                'endpoint': endpoint,
                                'status': response.status,
                                'response_status': result.get('status'),
                                'response_message': result.get('error_mess', result.get('message'))
                            })
                            return result
                        else:
                            error_text = await response.text()
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"HTTP {response.status}: {error_text}"
                            )
                            
            except Exception as e:
                last_exception = e
                
                # 特殊处理不同类型的异常
                if isinstance(e, asyncio.TimeoutError):
                    exception_details = f"请求超时 (超过{self.timeout.total}秒)"
                elif isinstance(e, aiohttp.ClientResponseError):
                    exception_details = f"HTTP错误: {e.status} - {e.message}"
                else:
                    exception_details = str(e) if str(e) else f"{type(e).__name__} occurred"
                
                self.logger.error(f"POST请求异常 (第{attempt + 1}次尝试): {exception_details}", extra_data={
                    'endpoint': endpoint,
                    'exception_type': type(e).__name__,
                    'exception_details': exception_details,
                    'attempt': attempt + 1,
                    'max_attempts': retries + 1
                })
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < retries:
                    retry_delay = self.retry_delay * (attempt + 1)  # 递增延迟
                    self.logger.info(f"等待 {retry_delay} 秒后重试")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    # 最后一次尝试失败，抛出异常
                    raise last_exception
        
        # 理论上不应该到达这里，但为了完整性
        raise last_exception
    
    async def get_request(self, 
                         endpoint: str,
                         params: Dict[str, Any] = None,
                         tokens: Dict[str, str] = None,
                         additional_headers: Dict[str, str] = None,
                         retries: int = None) -> Dict[str, Any]:
        """
        执行GET请求
        
        Args:
            endpoint: 完整的API端点URL
            params: 查询参数
            tokens: 认证Token字典（可选）
            additional_headers: 额外的请求头
            retries: 重试次数，None使用默认值
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        if retries is None:
            retries = self.max_retries
        
        last_exception = None
        
        for attempt in range(retries + 1):
            try:
                self.logger.info(f"执行GET请求 (第{attempt + 1}次尝试)", extra_data={
                    'endpoint': endpoint,
                    'params': params,
                    'attempt': attempt + 1
                })
                
                # 构建请求头
                headers = self.config['common_headers'].copy()
                if tokens:
                    # 添加JWT Token到Authorization头
                    if 'jwt_token' in tokens and tokens['jwt_token']:
                        headers['Authorization'] = tokens['jwt_token']
                    
                    # 添加其他认证头
                    headers.update({
                        'devicenumber': tokens.get('device_number', ''),
                        'token1-check': tokens.get('token1_check', ''),
                        'token2': tokens.get('token2', ''),
                        'token2-timestamp': tokens.get('token2_timestamp', ''),
                        'Scd': tokens.get('device_number', '')  # 从截图中看到Scd头也是必要的
                    })
                    
                    # 添加anticlimb_verify_code如果存在
                    if 'anticlimb_verify_code' in tokens and tokens['anticlimb_verify_code']:
                        headers['Sys'] = tokens['anticlimb_verify_code']
                
                if additional_headers:
                    headers.update(additional_headers)
                
                # 记录请求头信息（调试用）
                self.logger.debug(f"请求头信息", extra_data={
                    'headers_keys': list(headers.keys()),
                    'has_authorization': 'Authorization' in headers,
                    'auth_header_length': len(headers.get('Authorization', '')) if 'Authorization' in headers else 0
                })
                
                # 构建Cookie
                cookies = {}
                if tokens and 'cookies' in tokens:
                    cookies = tokens['cookies']
                    self.logger.debug("使用Token中的Cookie", extra_data={
                        'cookie_keys': list(cookies.keys()) if cookies else []
                    })
                
                # 发送请求
                async with aiohttp.ClientSession(timeout=self.timeout) as session:
                    async with session.get(
                        endpoint,
                        params=params,
                        headers=headers,
                        cookies=cookies
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()

                            # 检测登录失效
                            if self._is_auth_failed_response(result):
                                self.logger.warning("🔐 检测到登录失效响应", extra_data={
                                    'endpoint': endpoint,
                                    'response_status': result.get('status'),
                                    'error_code': result.get('errorCode'),
                                    'error_message': result.get('errorMess', result.get('msg'))
                                })
                                # 抛出特殊异常，便于上层处理
                                raise Exception(f"AUTH_FAILED: 登录状态失效，需要重新认证 - {result.get('errorMess', result.get('msg', ''))}")

                            self.logger.info("GET请求成功", extra_data={
                                'endpoint': endpoint,
                                'status': response.status
                            })
                            return result
                        else:
                            error_text = await response.text()
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"HTTP {response.status}: {error_text}"
                            )
                            
            except Exception as e:
                last_exception = e
                
                exception_details = str(e) if str(e) else f"{type(e).__name__} occurred"
                self.logger.error(f"GET请求异常 (第{attempt + 1}次尝试): {exception_details}", extra_data={
                    'endpoint': endpoint,
                    'exception_type': type(e).__name__,
                    'attempt': attempt + 1
                })
                
                if attempt < retries:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    raise last_exception
        
        raise last_exception
    
    async def download_file(self,
                           file_url: str,
                           save_path: Optional[str] = None,
                           use_temp_dir: bool = False,
                           tokens: Dict[str, str] = None) -> str:
        """
        下载文件
        
        Args:
            file_url: 文件URL
            save_path: 保存路径（可选）
            use_temp_dir: 是否使用临时目录
            tokens: 认证Token字典（可选）
            
        Returns:
            str: 下载文件的本地路径
        """
        try:
            self.logger.info(f"开始下载文件: {file_url}")
            
            # 确定保存路径
            if use_temp_dir or save_path is None:
                save_dir = Path(tempfile.gettempdir()) / "yimai_downloads"
            else:
                save_dir = Path(save_path).parent
            
            # 确保保存目录存在
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # 从URL提取文件名
            file_name = file_url.split('/')[-1]
            if '?' in file_name:
                file_name = file_name.split('?')[0]
            
            local_path = save_dir / file_name
            
            # 构建请求头
            headers = self.config['common_headers'].copy()
            if tokens:
                # 添加JWT Token到Authorization头
                if 'jwt_token' in tokens and tokens['jwt_token']:
                    headers['Authorization'] = tokens['jwt_token']
                
                # 添加其他认证头
                headers.update({
                    'devicenumber': tokens.get('device_number', ''),
                    'token1-check': tokens.get('token1_check', ''),
                    'token2': tokens.get('token2', ''),
                    'token2-timestamp': tokens.get('token2_timestamp', ''),
                    'Scd': tokens.get('device_number', '')  # 从截图中看到Scd头也是必要的
                })
                
                # 添加anticlimb_verify_code如果存在
                if 'anticlimb_verify_code' in tokens and tokens['anticlimb_verify_code']:
                    headers['Sys'] = tokens['anticlimb_verify_code']
            
            # 构建Cookie
            cookies = {}
            if tokens and 'cookies' in tokens:
                cookies = tokens['cookies']
                self.logger.debug("使用Token中的Cookie", extra_data={
                    'cookie_keys': list(cookies.keys()) if cookies else []
                })
            
            # 下载文件
            download_timeout = aiohttp.ClientTimeout(total=self.config['download_timeout'])
            async with aiohttp.ClientSession(timeout=download_timeout) as session:
                async with session.get(file_url, headers=headers, cookies=cookies) as response:
                    
                    if response.status == 200:
                        file_size = 0
                        with open(local_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)
                                file_size += len(chunk)
                        
                        self.logger.info(f"文件下载成功", extra_data={
                            'file_url': file_url,
                            'local_path': str(local_path),
                            'file_size': file_size
                        })
                        
                        return str(local_path)
                    else:
                        error_text = await response.text()
                        raise Exception(f"文件下载失败: HTTP {response.status}, {error_text}")
                        
        except Exception as e:
            self.logger.error(f"文件下载异常: {str(e)}", extra_data={
                'file_url': file_url,
                'save_path': save_path,
                'exception_type': type(e).__name__
            })
            raise
    
    async def get_file_content(self,
                              file_url: str,
                              tokens: Dict[str, str] = None) -> bytes:
        """
        获取文件内容（内存中处理）
        
        Args:
            file_url: 文件URL
            tokens: 认证Token字典（可选）
            
        Returns:
            bytes: 文件内容
        """
        download_retries = self.config.get('download_max_retries', 5)
        retry_delay = self.config.get('download_retry_delay', 5)
        chunk_size = self.config.get('download_chunk_size', 8192)
        max_file_size = self.config.get('max_file_size_mb', 512) * 1024 * 1024
        
        last_exception = None
        
        for attempt in range(download_retries + 1):
            try:
                attempt_info = f"第{attempt + 1}次尝试" if attempt > 0 else "首次尝试"
                self.logger.info(f"开始获取文件内容: {file_url} ({attempt_info})", extra_data={
                    'download_timeout': self.config['download_timeout'],
                    'timeout_minutes': self.config['download_timeout'] / 60,
                    'attempt': attempt + 1,
                    'max_attempts': download_retries + 1,
                    'retry_delay': retry_delay
                })
                
                # 构建请求头
                headers = self.config['common_headers'].copy()
                if tokens:
                    # 添加JWT Token到Authorization头
                    if 'jwt_token' in tokens and tokens['jwt_token']:
                        headers['Authorization'] = tokens['jwt_token']
                    
                    # 添加其他认证头
                    headers.update({
                        'devicenumber': tokens.get('device_number', ''),
                        'token1-check': tokens.get('token1_check', ''),
                        'token2': tokens.get('token2', ''),
                        'token2-timestamp': tokens.get('token2_timestamp', ''),
                        'Scd': tokens.get('device_number', '')  # 从截图中看到Scd头也是必要的
                    })
                    
                    # 添加anticlimb_verify_code如果存在
                    if 'anticlimb_verify_code' in tokens and tokens['anticlimb_verify_code']:
                        headers['Sys'] = tokens['anticlimb_verify_code']
                
                # 构建Cookie
                cookies = {}
                if tokens and 'cookies' in tokens:
                    cookies = tokens['cookies']
                    self.logger.debug("使用Token中的Cookie", extra_data={
                        'cookie_keys': list(cookies.keys()) if cookies else []
                    })
                
                # 获取文件内容（使用更长的超时时间）
                download_timeout = aiohttp.ClientTimeout(total=self.config['download_timeout'])
                async with aiohttp.ClientSession(timeout=download_timeout) as session:
                    async with session.get(file_url, headers=headers, cookies=cookies) as response:
                        
                        if response.status == 200:
                            # 检查文件大小
                            content_length = response.headers.get('content-length')
                            if content_length:
                                file_size = int(content_length)
                                if file_size > max_file_size:
                                    raise Exception(f"文件过大: {file_size / 1024 / 1024:.2f}MB, 限制: {self.config.get('max_file_size_mb', 512)}MB")
                                
                                self.logger.info(f"开始下载文件，预期大小: {file_size / 1024:.2f}KB")
                            
                            # 分块读取文件内容
                            content = bytearray()
                            downloaded_size = 0
                            
                            async for chunk in response.content.iter_chunked(chunk_size):
                                content.extend(chunk)
                                downloaded_size += len(chunk)
                                
                                # 检查是否超过大小限制
                                if downloaded_size > max_file_size:
                                    raise Exception(f"下载过程中文件大小超限: {downloaded_size / 1024 / 1024:.2f}MB")
                                
                                # 每下载10MB记录一次进度
                                if downloaded_size % (10 * 1024 * 1024) == 0:
                                    self.logger.debug(f"下载进度: {downloaded_size / 1024 / 1024:.2f}MB")
                            
                            final_content = bytes(content)
                            self.logger.info(f"文件内容获取成功", extra_data={
                                'file_url': file_url,
                                'content_size': len(final_content),
                                'content_size_mb': len(final_content) / 1024 / 1024,
                                'attempt': attempt + 1,
                                'success': True
                            })
                            return final_content
                        else:
                            error_text = await response.text()
                            raise Exception(f"文件内容获取失败: HTTP {response.status}, {error_text}")
                            
            except Exception as e:
                last_exception = e
                
                # 特别记录超时异常
                if 'timeout' in str(e).lower() or isinstance(e, asyncio.TimeoutError):
                    exception_details = f"文件下载超时: {str(e)}"
                    self.logger.error(exception_details, extra_data={
                        'file_url': file_url,
                        'exception_type': type(e).__name__,
                        'download_timeout': self.config['download_timeout'],
                        'timeout_minutes': self.config['download_timeout'] / 60,
                        'attempt': attempt + 1,
                        'max_attempts': download_retries + 1,
                        'suggestion': '文件可能较大或网络较慢，已自动重试'
                    })
                else:
                    exception_details = str(e) if str(e) else f"{type(e).__name__} occurred"
                    self.logger.error(f"文件下载异常: {exception_details}", extra_data={
                        'file_url': file_url,
                        'exception_type': type(e).__name__,
                        'exception_details': exception_details,
                        'attempt': attempt + 1,
                        'max_attempts': download_retries + 1
                    })
                
                # 如果还有重试机会，等待后重试
                if attempt < download_retries:
                    self.logger.info(f"等待 {retry_delay} 秒后重试...", extra_data={
                        'retry_delay': retry_delay,
                        'remaining_attempts': download_retries - attempt
                    })
                    await asyncio.sleep(retry_delay)
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"文件下载最终失败，已尝试 {download_retries + 1} 次", extra_data={
                        'file_url': file_url,
                        'total_attempts': download_retries + 1,
                        'final_exception': str(last_exception) if last_exception else 'Unknown error'
                    })
        
        # 抛出最后的异常
        if last_exception:
            raise last_exception
        else:
            raise Exception("文件下载失败，原因未知")
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新字典
        """
        self.config.update(config_updates)
        
        # 更新超时配置
        if 'request_timeout' in config_updates:
            self.timeout = aiohttp.ClientTimeout(total=self.config['request_timeout'])
        
        if 'max_retries' in config_updates:
            self.max_retries = self.config['max_retries']
        
        if 'retry_delay' in config_updates:
            self.retry_delay = self.config['retry_delay']
        
        self.logger.info("HTTP客户端配置已更新", extra_data={
            'updated_keys': list(config_updates.keys())
        })
    
    def get_full_url(self, endpoint_path: str) -> str:
        """
        获取完整的URL
        
        Args:
            endpoint_path: 端点路径
            
        Returns:
            str: 完整的URL
        """
        return f"{self.config['base_url']}{endpoint_path}" 