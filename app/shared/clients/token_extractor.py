"""
通用Token提取器

从Playwright页面中提取各种认证Token的通用工具：
- JWT <PERSON> (从Cookie中提取)
- Token1-Check (从localStorage或页面中提取)  
- DeviceNumber (设备指纹)
- Token2 相关信息
- Anticlimb_verify_code (反爬虫验证码)

重构说明：
- 基于export_publish_list/token_extractor.py重构
- 增加了通用性和可配置性
- 保持向后兼容性
"""

import logging
import json
import re
import time
from typing import Dict, Optional, Any, List
from playwright.async_api import Page


class TokenExtractor:
    """
    通用Token提取器
    
    从Playwright页面中提取各种认证信息的通用工具
    支持配置化的Token提取规则
    """
    
    def __init__(self, logger: logging.Logger = None, config: Dict[str, Any] = None):
        """
        初始化Token提取器
        
        Args:
            logger: 日志记录器
            config: 自定义配置，可覆盖默认配置
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.config = self._get_default_config()
        
        # 合并自定义配置
        if config:
            self.config.update(config)
        
        self.logger.info("通用Token提取器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # Cookie配置
            'cookie_names': {
                'user_data': 'POS_COOKIE_WEBFRONT_userdata',
                'session': 'PHPSESSID'
            },
            
            # localStorage键名配置
            'storage_keys': {
                'token1_check': ['token1_check', 'Token1-Check', 'tokenCheck', 'checkToken'],
                'device_number': ['device_number', 'deviceNumber', 'deviceId', 'fingerprint', 'device_fingerprint'],
                'token2': ['token2', 'Token2', 'secondToken'],
                'token2_timestamp': ['token2_timestamp', 'tokenTimestamp'],
                'anticlimb_code': ['anticlimb_verify_code', 'verifyCode', 'captcha', 'anticlimb'],
                'sign': ['sign', 'signature', 'sig', 'auth_sign']
            },
            
            # 页面变量配置
            'page_variables': {
                'jwt_token': [
                    'window.jwt_token',
                    'window.userInfo.token', 
                    'window.globalToken'
                ],
                'token1_check': [
                    'window.token1Check',
                    'window.checkToken',
                    'window.authToken'
                ],
                'device_number': [
                    'window.deviceNumber',
                    'window.deviceId',
                    'window.fingerprint'
                ],
                'anticlimb_code': [
                    'window.verifyCode',
                    'window.anticlimbCode',
                    'window.captcha'
                ],
                'sign': [
                    'window.sign',
                    'window.signature',
                    'window.authSign'
                ]
            },
            
            # 页面选择器配置
            'selectors': {
                'anticlimb_code': [
                    'input[name="anticlimb_verify_code"]',
                    'input[name="verifyCode"]', 
                    '#anticlimb_verify_code',
                    '#verifyCode'
                ]
            },
            
            # JWT Token验证配置
            'jwt_validation': {
                'min_length': 100,
                'pattern': r'eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+'
            }
        }
    
    async def extract_all_tokens(self, page: Page) -> Dict[str, str]:
        """
        提取所有必需的Token信息
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Dict[str, str]: 包含所有Token的字典
        """
        try:
            self.logger.info("开始提取所有Token信息")
            
            tokens = {}
            
            # 1. 提取JWT Token
            jwt_token = await self.extract_jwt_token(page)
            tokens['jwt_token'] = jwt_token or ''
            
            # 2. 提取Token1-Check
            token1_check = await self.extract_token1_check(page)
            tokens['token1_check'] = token1_check or ''
            
            # 3. 提取设备指纹
            device_number = await self.extract_device_number(page)
            tokens['device_number'] = device_number or ''
            
            # 4. 提取Token2相关
            token2_info = await self.extract_token2_info(page)
            tokens.update(token2_info)
            
            # 5. 提取反爬虫验证码
            anticlimb_code = await self.extract_anticlimb_verify_code(page)
            tokens['anticlimb_verify_code'] = anticlimb_code or ''
            
            # 6. 提取签名
            sign = await self.extract_sign(page)
            tokens['sign'] = sign or ''
            
            # 7. 提取Cookie
            cookies = await self.extract_cookies(page)
            tokens['cookies'] = cookies
            
            # 记录提取结果
            self.logger.info("Token提取完成", extra_data={
                'has_jwt_token': bool(tokens.get('jwt_token')),
                'has_token1_check': bool(tokens.get('token1_check')),
                'has_device_number': bool(tokens.get('device_number')),
                'has_token2': bool(tokens.get('token2')),
                'has_anticlimb_code': bool(tokens.get('anticlimb_verify_code')),
                'has_sign': bool(tokens.get('sign')),
                'has_cookies': bool(tokens.get('cookies')),
                'cookie_count': len(tokens.get('cookies', {}))
            })

            # 详细日志：打印所有提取的Token详细信息（脱敏）
            self.logger.debug("🔍 Token提取详细信息:")
            for key, value in tokens.items():
                if key == 'cookies':
                    # Cookie信息单独处理
                    if value:
                        self.logger.debug(f"  cookies: {list(value.keys())}")
                        for cookie_name, cookie_value in value.items():
                            if len(str(cookie_value)) > 20:
                                masked_cookie = f"{str(cookie_value)[:10]}...{str(cookie_value)[-10:]}"
                            else:
                                masked_cookie = str(cookie_value)
                            self.logger.debug(f"    {cookie_name}: {masked_cookie}")
                    else:
                        self.logger.debug(f"  cookies: None")
                elif key in ['jwt_token', 'token1_check', 'token2', 'anticlimb_verify_code', 'sign']:
                    # 敏感信息脱敏
                    if value and len(str(value)) > 20:
                        masked_value = f"{str(value)[:10]}...{str(value)[-10:]}"
                    else:
                        masked_value = str(value)
                    self.logger.debug(f"  {key}: {masked_value}")
                else:
                    # 非敏感信息直接显示
                    self.logger.debug(f"  {key}: {value}")
            
            return tokens
            
        except Exception as e:
            self.logger.error(f"Token提取异常: {e}")
            raise
    
    async def extract_jwt_token(self, page: Page) -> Optional[str]:
        """
        从Cookie或页面变量中提取JWT Token
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[str]: JWT Token
        """
        try:
            self.logger.debug("开始提取JWT Token")
            
            # 方法1: 从Cookie中提取
            jwt_token = await self._extract_jwt_from_cookie(page)
            if jwt_token:
                self.logger.info("从Cookie提取JWT Token成功", extra_data={
                    'jwt_token_preview': jwt_token[:50] + '...' if len(jwt_token) > 50 else jwt_token,
                    'jwt_token_length': len(jwt_token),
                    'source': 'cookie'
                })
                return jwt_token
            
            # 方法2: 从页面变量中提取
            jwt_token = await self._extract_jwt_from_page_vars(page)
            if jwt_token:
                self.logger.info("从页面变量提取JWT Token成功", extra_data={
                    'jwt_token_preview': jwt_token[:50] + '...' if len(jwt_token) > 50 else jwt_token,
                    'jwt_token_length': len(jwt_token),
                    'source': 'page_variables'
                })
                return jwt_token
            
            self.logger.warning("未找到JWT Token")
            return None
            
        except Exception as e:
            self.logger.error(f"JWT Token提取异常: {e}")
            return None
    
    async def _extract_jwt_from_cookie(self, page: Page) -> Optional[str]:
        """从Cookie中提取JWT Token"""
        try:
            cookies = await page.context.cookies()
            
            for cookie_name in self.config['cookie_names'].values():
                for cookie in cookies:
                    if cookie['name'] == cookie_name:
                        cookie_value = cookie['value']
                        self.logger.debug(f"找到Cookie {cookie_name}: {cookie_value[:50]}...")
                        
                        jwt_token = self._parse_jwt_from_cookie(cookie_value)
                        if jwt_token:
                            return jwt_token
            
            return None
            
        except Exception as e:
            self.logger.error(f"从Cookie提取JWT Token异常: {e}")
            return None
    
    def _parse_jwt_from_cookie(self, cookie_value: str) -> Optional[str]:
        """从Cookie值中解析JWT Token"""
        try:
            # URL解码
            import urllib.parse
            decoded_value = urllib.parse.unquote(cookie_value)
            
            # 查找token字段 (PHP序列化格式)
            token_pattern = r's:5:"token";s:\d+:"([^"]+)"'
            match = re.search(token_pattern, decoded_value)
            
            if match:
                jwt_token = match.group(1)
                if self._validate_jwt_format(jwt_token):
                    return jwt_token
            
            # 尝试直接正则匹配JWT格式
            jwt_pattern = self.config['jwt_validation']['pattern']
            jwt_match = re.search(jwt_pattern, decoded_value)
            if jwt_match:
                jwt_token = jwt_match.group(0)
                if self._validate_jwt_format(jwt_token):
                    return jwt_token
            
            return None
            
        except Exception as e:
            self.logger.error(f"Cookie解析异常: {e}")
            return None
    
    async def _extract_jwt_from_page_vars(self, page: Page) -> Optional[str]:
        """从页面JavaScript变量中提取JWT Token"""
        try:
            jwt_vars = self.config['page_variables']['jwt_token']
            
            for var_expr in jwt_vars:
                try:
                    result = await page.evaluate(f'() => {var_expr}')
                    if result and isinstance(result, str) and self._validate_jwt_format(result):
                        self.logger.debug(f"从 {var_expr} 获取到JWT Token")
                        return result
                except:
                    continue
            
            # 尝试从localStorage获取
            for key in ['jwt_token', 'token']:
                try:
                    result = await page.evaluate(f'() => localStorage.getItem("{key}")')
                    if result and self._validate_jwt_format(result):
                        return result
                except:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"页面变量提取异常: {e}")
            return None
    
    def _validate_jwt_format(self, jwt_token: str) -> bool:
        """验证JWT Token格式"""
        if not jwt_token:
            return False
        
        # 长度检查
        if len(jwt_token) < self.config['jwt_validation']['min_length']:
            return False
        
        # 格式检查
        pattern = self.config['jwt_validation']['pattern']
        return bool(re.match(pattern, jwt_token))
    
    async def extract_token1_check(self, page: Page) -> Optional[str]:
        """
        提取Token1-Check
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[str]: Token1-Check
        """
        try:
            self.logger.debug("开始提取Token1-Check")
            
            # 从localStorage获取
            for key in self.config['storage_keys']['token1_check']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    self.logger.info(f"从localStorage.{key}获取Token1-Check成功")
                    return result
            
            # 从sessionStorage获取
            for key in self.config['storage_keys']['token1_check']:
                result = await self._get_from_storage(page, key, 'sessionStorage')
                if result:
                    self.logger.info(f"从sessionStorage.{key}获取Token1-Check成功")
                    return result
            
            # 从页面变量获取
            for var_expr in self.config['page_variables']['token1_check']:
                try:
                    result = await page.evaluate(f'() => {var_expr}')
                    if result:
                        self.logger.info(f"从 {var_expr} 获取Token1-Check成功")
                        return result
                except:
                    continue
            
            self.logger.warning("未找到Token1-Check")
            return None
            
        except Exception as e:
            self.logger.error(f"Token1-Check提取异常: {e}")
            return None
    
    async def extract_device_number(self, page: Page) -> Optional[str]:
        """
        提取设备指纹
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[str]: 设备指纹
        """
        try:
            self.logger.debug("开始提取设备指纹")
            
            # 从localStorage获取
            for key in self.config['storage_keys']['device_number']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    self.logger.info(f"从localStorage.{key}获取设备指纹成功")
                    return result
            
            # 从页面变量获取
            for var_expr in self.config['page_variables']['device_number']:
                try:
                    result = await page.evaluate(f'() => {var_expr}')
                    if result:
                        self.logger.info(f"从 {var_expr} 获取设备指纹成功")
                        return result
                except:
                    continue
            
            # 生成默认设备指纹
            default_device_number = self._generate_default_device_number()
            self.logger.warning(f"未找到设备指纹，使用默认值: {default_device_number}")
            return default_device_number
            
        except Exception as e:
            self.logger.error(f"设备指纹提取异常: {e}")
            return self._generate_default_device_number()
    
    def _generate_default_device_number(self) -> str:
        """生成默认设备指纹"""
        import hashlib
        import random
        
        timestamp = str(int(time.time()))
        random_str = str(random.randint(100000, 999999))
        
        device_data = f"device_{timestamp}_{random_str}"
        device_hash = hashlib.md5(device_data.encode()).hexdigest()
        
        return device_hash[:32]
    
    async def extract_token2_info(self, page: Page) -> Dict[str, str]:
        """
        提取Token2相关信息
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Dict[str, str]: Token2相关信息
        """
        try:
            self.logger.debug("开始提取Token2信息")
            
            token2_info = {
                'token2': '',
                'token2_timestamp': str(int(time.time()))
            }
            
            # 提取Token2
            for key in self.config['storage_keys']['token2']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    token2_info['token2'] = result
                    self.logger.info(f"从localStorage.{key}获取Token2成功")
                    break
            
            # 提取Token2时间戳
            for key in self.config['storage_keys']['token2_timestamp']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    token2_info['token2_timestamp'] = result
                    self.logger.info(f"从localStorage.{key}获取Token2时间戳成功")
                    break
            
            return token2_info
            
        except Exception as e:
            self.logger.error(f"Token2信息提取异常: {e}")
            return {
                'token2': '',
                'token2_timestamp': str(int(time.time()))
            }
    
    async def extract_anticlimb_verify_code(self, page: Page) -> Optional[str]:
        """
        提取反爬虫验证码
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[str]: 反爬虫验证码
        """
        try:
            self.logger.debug("开始提取反爬虫验证码")
            
            # 从localStorage获取
            for key in self.config['storage_keys']['anticlimb_code']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    self.logger.info(f"从localStorage.{key}获取验证码成功")
                    return result
            
            # 从页面变量获取
            for var_expr in self.config['page_variables']['anticlimb_code']:
                try:
                    result = await page.evaluate(f'() => {var_expr}')
                    if result:
                        self.logger.info(f"从 {var_expr} 获取验证码成功")
                        return result
                except:
                    continue
            
            # 从页面元素获取
            for selector in self.config['selectors']['anticlimb_code']:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        value = await element.get_attribute('value')
                        if value:
                            self.logger.info(f"从元素 {selector} 获取验证码成功")
                            return value
                except:
                    continue
            
            self.logger.debug("未找到反爬虫验证码")
            return None
            
        except Exception as e:
            self.logger.error(f"反爬虫验证码提取异常: {e}")
            return None
    
    async def _get_from_storage(self, page: Page, key: str, storage_type: str = 'localStorage') -> Optional[str]:
        """从存储中获取值"""
        try:
            result = await page.evaluate(f'() => {storage_type}.getItem("{key}")')
            return result if result else None
        except:
            return None
    
    async def validate_tokens(self, tokens: Dict[str, str]) -> bool:
        """
        验证Token有效性
        
        Args:
            tokens: Token字典
            
        Returns:
            bool: 是否有效
        """
        try:
            self.logger.debug("开始验证Token有效性")
            
            # 检查必须的Token (保持与原逻辑一致，只验证jwt_token)
            required_tokens = ['jwt_token']
            for token_name in required_tokens:
                if not tokens.get(token_name):
                    self.logger.error(f"缺少必须的Token: {token_name}")
                    return False
            
            # 验证JWT Token格式
            jwt_token = tokens.get('jwt_token')
            if not self._validate_jwt_format(jwt_token):
                self.logger.error("JWT Token格式无效")
                return False
            
            self.logger.info("Token验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"Token验证异常: {e}")
            return False
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新字典
        """
        self.config.update(config_updates)
        self.logger.info("Token提取器配置已更新", extra_data={
            'updated_keys': list(config_updates.keys())
        })
    
    async def extract_cookies(self, page: Page) -> Dict[str, str]:
        """
        提取页面Cookie
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Dict[str, str]: Cookie字典
        """
        try:
            self.logger.debug("开始提取Cookie")
            
            # 获取所有Cookie
            cookies = await page.context.cookies()
            
            # 转换为字典格式
            cookie_dict = {}
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
            
            self.logger.info(f"成功提取 {len(cookie_dict)} 个Cookie", extra_data={
                'cookie_names': list(cookie_dict.keys())
            })
            
            return cookie_dict
            
        except Exception as e:
            self.logger.error(f"Cookie提取异常: {str(e)}")
            return {}

    async def extract_sign(self, page: Page) -> Optional[str]:
        """
        提取签名
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[str]: 签名
        """
        try:
            self.logger.debug("开始提取签名")
            
            # 从localStorage获取
            for key in self.config['storage_keys']['sign']:
                result = await self._get_from_storage(page, key, 'localStorage')
                if result:
                    self.logger.info(f"从localStorage.{key}获取签名成功")
                    return result
            
            # 从sessionStorage获取
            for key in self.config['storage_keys']['sign']:
                result = await self._get_from_storage(page, key, 'sessionStorage')
                if result:
                    self.logger.info(f"从sessionStorage.{key}获取签名成功")
                    return result
            
            # 从页面变量获取
            for var_expr in self.config['page_variables']['sign']:
                try:
                    result = await page.evaluate(f'() => {var_expr}')
                    if result:
                        self.logger.info(f"从 {var_expr} 获取签名成功")
                        return result
                except:
                    continue
            
            self.logger.warning("未找到签名")
            return None
            
        except Exception as e:
            self.logger.error(f"签名提取异常: {e}")
            return None 